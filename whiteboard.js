class InfiniteWhiteboard {
    constructor() {
        this.canvas = document.getElementById('whiteboard-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.container = document.getElementById('canvas-container');
        this.toolbox = document.getElementById('toolbox');
        this.toolOptions = document.getElementById('tool-options');
        
        // Canvas state
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;
        this.isDragging = false;
        this.isDrawing = false;
        this.lastX = 0;
        this.lastY = 0;
        
        // Tools
        this.currentTool = 'hand';
        this.brushSize = 3;
        this.brushColor = '#000000';
        this.fontSize = 16;
        
        // Elements
        this.elements = [];
        this.selectedElement = null;
        this.isResizing = false;
        this.resizeHandle = null;
        
        // File tracking
        this.totalFileSize = 0;
        
        this.init();
    }
    
    init() {
        this.setupCanvas();
        this.setupEventListeners();
        this.setupToolbox();
        this.drawGrid();
        this.showStartingFrame();
    }
    
    setupCanvas() {
        this.canvas.width = window.innerWidth * 3;
        this.canvas.height = window.innerHeight * 3;
        this.canvas.style.left = -window.innerWidth + 'px';
        this.canvas.style.top = -window.innerHeight + 'px';
    }
    
    setupEventListeners() {
        // Mouse events
        this.container.addEventListener('mousedown', this.handleMouseDown.bind(this));
        this.container.addEventListener('mousemove', this.handleMouseMove.bind(this));
        this.container.addEventListener('mouseup', this.handleMouseUp.bind(this));
        this.container.addEventListener('wheel', this.handleWheel.bind(this));
        
        // Tool events
        document.querySelectorAll('.tool-button').forEach(button => {
            button.addEventListener('click', this.selectTool.bind(this));
        });
        
        // Tool options
        document.getElementById('brush-size').addEventListener('input', (e) => {
            this.brushSize = parseInt(e.target.value);
            document.getElementById('size-value').textContent = this.brushSize;
        });
        
        document.getElementById('brush-color').addEventListener('change', (e) => {
            this.brushColor = e.target.value;
        });
        
        document.getElementById('font-size').addEventListener('change', (e) => {
            this.fontSize = parseInt(e.target.value);
        });
        
        // Reset view
        document.getElementById('reset-view').addEventListener('click', this.resetView.bind(this));
        
        // File inputs
        document.getElementById('image-input').addEventListener('change', this.handleImageUpload.bind(this));
        document.getElementById('video-input').addEventListener('change', this.handleVideoUpload.bind(this));
        
        // Window resize
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // Keyboard events
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }
    
    setupToolbox() {
        this.makeToolboxDraggable();
    }
    
    makeToolboxDraggable() {
        let isDragging = false;
        let startX, startY, initialX, initialY;
        
        this.toolbox.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('tool-button')) return;
            
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            
            const rect = this.toolbox.getBoundingClientRect();
            initialX = rect.left;
            initialY = rect.top;
            
            this.toolbox.style.cursor = 'grabbing';
        });
        
        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            this.toolbox.style.left = (initialX + deltaX) + 'px';
            this.toolbox.style.top = (initialY + deltaY) + 'px';
            this.toolbox.style.transform = 'none';
        });
        
        document.addEventListener('mouseup', () => {
            isDragging = false;
            this.toolbox.style.cursor = 'move';
        });
    }
    
    drawGrid() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.strokeStyle = '#e0e0e0';
        this.ctx.lineWidth = 1;
        
        const gridSize = 10;
        const startX = Math.floor(-this.offsetX / this.scale / gridSize) * gridSize;
        const startY = Math.floor(-this.offsetY / this.scale / gridSize) * gridSize;
        const endX = startX + (this.canvas.width / this.scale) + gridSize;
        const endY = startY + (this.canvas.height / this.scale) + gridSize;
        
        // Draw + symbols at grid intersections
        this.ctx.font = '8px Arial';
        this.ctx.fillStyle = '#d0d0d0';
        
        for (let x = startX; x < endX; x += gridSize) {
            for (let y = startY; y < endY; y += gridSize) {
                const screenX = (x * this.scale) + this.offsetX;
                const screenY = (y * this.scale) + this.offsetY;
                
                if (screenX >= 0 && screenX <= this.canvas.width && 
                    screenY >= 0 && screenY <= this.canvas.height) {
                    this.ctx.fillText('+', screenX - 3, screenY + 3);
                }
            }
        }
    }
    
    showStartingFrame() {
        const frame = document.querySelector('.starting-frame');
        frame.style.width = window.innerWidth + 'px';
        frame.style.height = window.innerHeight + 'px';
        frame.style.left = '0px';
        frame.style.top = '0px';
    }
    
    selectTool(e) {
        document.querySelectorAll('.tool-button').forEach(btn => btn.classList.remove('active'));
        e.target.classList.add('active');
        this.currentTool = e.target.dataset.tool;
        
        // Show/hide tool options
        if (['pen', 'text'].includes(this.currentTool)) {
            this.toolOptions.classList.add('visible');
        } else {
            this.toolOptions.classList.remove('visible');
        }
        
        // Handle file uploads
        if (this.currentTool === 'image') {
            document.getElementById('image-input').click();
        } else if (this.currentTool === 'video') {
            document.getElementById('video-input').click();
        }
        
        // Update cursor
        this.updateCursor();
    }
    
    updateCursor() {
        const cursors = {
            hand: 'grab',
            select: 'default',
            pen: 'crosshair',
            text: 'text',
            crop: 'crosshair'
        };
        
        this.container.style.cursor = cursors[this.currentTool] || 'default';
    }
    
    handleMouseDown(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left - this.offsetX) / this.scale;
        const y = (e.clientY - rect.top - this.offsetY) / this.scale;
        
        this.lastX = x;
        this.lastY = y;
        
        if (this.currentTool === 'hand') {
            this.isDragging = true;
            this.container.classList.add('grabbing');
        } else if (this.currentTool === 'pen') {
            this.isDrawing = true;
            this.ctx.beginPath();
            this.ctx.moveTo(x, y);
        } else if (this.currentTool === 'text') {
            this.addText(x, y);
        } else if (this.currentTool === 'select') {
            this.handleSelection(x, y);
        }
    }
    
    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left - this.offsetX) / this.scale;
        const y = (e.clientY - rect.top - this.offsetY) / this.scale;
        
        if (this.isDragging && this.currentTool === 'hand') {
            const deltaX = (x - this.lastX) * this.scale;
            const deltaY = (y - this.lastY) * this.scale;
            
            this.offsetX += deltaX;
            this.offsetY += deltaY;
            
            this.canvas.style.left = this.offsetX + 'px';
            this.canvas.style.top = this.offsetY + 'px';
            
            this.drawGrid();
        } else if (this.isDrawing && this.currentTool === 'pen') {
            this.ctx.lineWidth = this.brushSize;
            this.ctx.strokeStyle = this.brushColor;
            this.ctx.lineCap = 'round';
            this.ctx.lineJoin = 'round';
            
            this.ctx.lineTo(x, y);
            this.ctx.stroke();
            this.ctx.beginPath();
            this.ctx.moveTo(x, y);
        }
        
        this.lastX = x;
        this.lastY = y;
    }
    
    handleMouseUp() {
        this.isDragging = false;
        this.isDrawing = false;
        this.container.classList.remove('grabbing');
    }
    
    handleWheel(e) {
        e.preventDefault();
        
        const rect = this.canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        const wheel = e.deltaY < 0 ? 1 : -1;
        const zoom = Math.exp(wheel * 0.1);
        const newScale = Math.max(0.1, Math.min(5, this.scale * zoom));
        
        if (newScale !== this.scale) {
            const factor = newScale / this.scale;
            
            this.offsetX = mouseX - (mouseX - this.offsetX) * factor;
            this.offsetY = mouseY - (mouseY - this.offsetY) * factor;
            
            this.scale = newScale;
            
            this.canvas.style.left = this.offsetX + 'px';
            this.canvas.style.top = this.offsetY + 'px';
            this.canvas.style.transform = `scale(${this.scale})`;
            
            this.drawGrid();
        }
    }
    
    resetView() {
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;
        
        this.canvas.style.left = -window.innerWidth + 'px';
        this.canvas.style.top = -window.innerHeight + 'px';
        this.canvas.style.transform = 'scale(1)';
        
        this.drawGrid();
        this.showStartingFrame();
    }
    
    handleResize() {
        this.setupCanvas();
        this.drawGrid();
        this.showStartingFrame();
    }
    
    handleKeyDown(e) {
        if (e.key === 'Delete' && this.selectedElement) {
            this.deleteSelectedElement();
        }
    }
    
    addText(x, y) {
        const text = prompt('Enter text:');
        if (text) {
            const element = {
                type: 'text',
                x: x,
                y: y,
                width: text.length * this.fontSize * 0.6,
                height: this.fontSize * 1.2,
                content: text,
                fontSize: this.fontSize,
                color: this.brushColor
            };
            
            this.elements.push(element);
            this.renderElements();
        }
    }
    
    handleImageUpload(e) {
        const file = e.target.files[0];
        if (file) {
            this.trackFileSize(file);
            
            const reader = new FileReader();
            reader.onload = (event) => {
                const img = new Image();
                img.onload = () => {
                    const element = {
                        type: 'image',
                        x: 100,
                        y: 100,
                        width: img.width,
                        height: img.height,
                        src: event.target.result,
                        file: file
                    };
                    
                    this.elements.push(element);
                    this.renderElements();
                };
                img.src = event.target.result;
            };
            reader.readAsDataURL(file);
        }
    }
    
    handleVideoUpload(e) {
        const file = e.target.files[0];
        if (file) {
            this.trackFileSize(file);
            
            const reader = new FileReader();
            reader.onload = (event) => {
                const element = {
                    type: 'video',
                    x: 100,
                    y: 100,
                    width: 320,
                    height: 240,
                    src: event.target.result,
                    file: file
                };
                
                this.elements.push(element);
                this.renderElements();
            };
            reader.readAsDataURL(file);
        }
    }
    
    trackFileSize(file) {
        this.totalFileSize += file.size;
        console.log(`File uploaded: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
        console.log(`Total file size: ${(this.totalFileSize / 1024 / 1024).toFixed(2)} MB`);
    }
    
    renderElements() {
        // This would render elements on the canvas or as DOM elements
        // For now, we'll create DOM elements for images and videos
        this.elements.forEach((element, index) => {
            if (!element.domElement) {
                this.createElement(element, index);
            }
        });
    }
    
    createElement(element, index) {
        const div = document.createElement('div');
        div.className = 'canvas-element';
        div.style.left = (element.x * this.scale + this.offsetX) + 'px';
        div.style.top = (element.y * this.scale + this.offsetY) + 'px';
        div.style.width = (element.width * this.scale) + 'px';
        div.style.height = (element.height * this.scale) + 'px';
        
        if (element.type === 'image') {
            const img = document.createElement('img');
            img.src = element.src;
            div.appendChild(img);
        } else if (element.type === 'video') {
            const video = document.createElement('video');
            video.src = element.src;
            video.controls = false;
            div.appendChild(video);
            
            // Add custom controls
            this.addVideoControls(div, video);
        }
        
        element.domElement = div;
        this.container.appendChild(div);
    }
    
    addVideoControls(container, video) {
        const controls = document.createElement('div');
        controls.className = 'video-controls';
        
        const playButton = document.createElement('button');
        playButton.className = 'play-button';
        playButton.textContent = '▶️';
        playButton.onclick = () => {
            if (video.paused) {
                video.play();
                playButton.textContent = '⏸️';
            } else {
                video.pause();
                playButton.textContent = '▶️';
            }
        };
        
        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar';
        const progressFill = document.createElement('div');
        progressFill.className = 'progress-fill';
        progressBar.appendChild(progressFill);
        
        const timeDisplay = document.createElement('span');
        timeDisplay.className = 'time-display';
        timeDisplay.textContent = '0:00 / 0:00';
        
        video.addEventListener('timeupdate', () => {
            const progress = (video.currentTime / video.duration) * 100;
            progressFill.style.width = progress + '%';
            
            const current = this.formatTime(video.currentTime);
            const duration = this.formatTime(video.duration);
            timeDisplay.textContent = `${current} / ${duration}`;
        });
        
        controls.appendChild(playButton);
        controls.appendChild(progressBar);
        controls.appendChild(timeDisplay);
        container.appendChild(controls);
    }
    
    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }
    
    handleSelection(x, y) {
        // Find element at position
        const element = this.findElementAt(x, y);
        
        if (this.selectedElement) {
            this.selectedElement.domElement.classList.remove('selected');
        }
        
        if (element) {
            this.selectedElement = element;
            element.domElement.classList.add('selected');
            this.addResizeHandles(element);
        } else {
            this.selectedElement = null;
        }
    }
    
    findElementAt(x, y) {
        return this.elements.find(element => 
            x >= element.x && x <= element.x + element.width &&
            y >= element.y && y <= element.y + element.height
        );
    }
    
    addResizeHandles(element) {
        // Remove existing handles
        document.querySelectorAll('.resize-handle').forEach(handle => handle.remove());
        
        const handles = ['nw', 'ne', 'sw', 'se'];
        handles.forEach(position => {
            const handle = document.createElement('div');
            handle.className = `resize-handle ${position}`;
            element.domElement.appendChild(handle);
        });
    }
    
    deleteSelectedElement() {
        if (this.selectedElement) {
            const index = this.elements.indexOf(this.selectedElement);
            if (index > -1) {
                this.selectedElement.domElement.remove();
                this.elements.splice(index, 1);
                this.selectedElement = null;
            }
        }
    }
}

// Initialize the whiteboard when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new InfiniteWhiteboard();
});
