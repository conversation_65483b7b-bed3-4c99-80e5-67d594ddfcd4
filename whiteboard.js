class InfiniteWhiteboard {
    constructor() {
        this.canvas = document.getElementById('whiteboard-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.container = document.getElementById('canvas-container');
        this.toolbox = document.getElementById('toolbox');
        this.toolOptions = document.getElementById('tool-options');

        // Canvas state
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;
        this.isDragging = false;
        this.isDrawing = false;
        this.lastX = 0;
        this.lastY = 0;
        this.isMiddleMouseDragging = false;

        // Tools
        this.currentTool = 'hand';
        this.brushSize = 3;
        this.brushColor = '#000000';
        this.fontSize = 16;

        // Elements
        this.elements = [];
        this.selectedElement = null;
        this.isResizing = false;
        this.resizeHandle = null;
        this.resizeStartX = 0;
        this.resizeStartY = 0;
        this.resizeStartWidth = 0;
        this.resizeStartHeight = 0;
        this.resizeStartElementX = 0;
        this.resizeStartElementY = 0;

        // File tracking
        this.totalFileSize = 0;

        // Pending upload (for hover effect)
        this.pendingUpload = null;
        this.mouseX = 0;
        this.mouseY = 0;

        this.init();
    }
    
    init() {
        this.setupCanvas();
        this.setupEventListeners();
        this.setupToolbox();
        this.drawGrid();
    }
    
    setupCanvas() {
        // Set canvas to cover the viewport
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;

        // Position canvas at viewport origin
        this.offsetX = 0;
        this.offsetY = 0;

        this.canvas.style.left = '0px';
        this.canvas.style.top = '0px';
    }
    
    setupEventListeners() {
        // Mouse events
        this.container.addEventListener('mousedown', this.handleMouseDown.bind(this));
        this.container.addEventListener('mousemove', this.handleMouseMove.bind(this));
        this.container.addEventListener('mouseup', this.handleMouseUp.bind(this));
        this.container.addEventListener('wheel', this.handleWheel.bind(this));
        this.container.addEventListener('contextmenu', (e) => e.preventDefault()); // Prevent right-click menu

        // Tool events
        document.querySelectorAll('.tool-button').forEach(button => {
            button.addEventListener('click', this.selectTool.bind(this));
        });

        // Tool options
        document.getElementById('brush-size').addEventListener('input', (e) => {
            this.brushSize = parseInt(e.target.value);
            document.getElementById('size-value').textContent = this.brushSize;
        });

        document.getElementById('brush-color').addEventListener('change', (e) => {
            this.brushColor = e.target.value;
        });

        document.getElementById('font-size').addEventListener('change', (e) => {
            this.fontSize = parseInt(e.target.value);
        });

        // Reset view
        document.getElementById('reset-view').addEventListener('click', this.resetView.bind(this));

        // File inputs
        document.getElementById('image-input').addEventListener('change', this.handleImageUpload.bind(this));
        document.getElementById('video-input').addEventListener('change', this.handleVideoUpload.bind(this));

        // Window resize
        window.addEventListener('resize', this.handleResize.bind(this));

        // Keyboard events
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }
    
    setupToolbox() {
        this.makeToolboxDraggable();
    }
    
    makeToolboxDraggable() {
        let isDragging = false;
        let startX, startY, initialX, initialY;
        
        this.toolbox.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('tool-button')) return;
            
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            
            const rect = this.toolbox.getBoundingClientRect();
            initialX = rect.left;
            initialY = rect.top;
            
            this.toolbox.style.cursor = 'grabbing';
        });
        
        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            this.toolbox.style.left = (initialX + deltaX) + 'px';
            this.toolbox.style.top = (initialY + deltaY) + 'px';
            this.toolbox.style.transform = 'none';
        });
        
        document.addEventListener('mouseup', () => {
            isDragging = false;
            this.toolbox.style.cursor = 'move';
        });
    }
    
    drawGrid() {
        // Clear the entire canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Save context for transformations
        this.ctx.save();

        // Apply scale and translation
        this.ctx.scale(this.scale, this.scale);
        this.ctx.translate(this.offsetX / this.scale, this.offsetY / this.scale);

        // Draw initial viewport border (what users see when they first load)
        // This represents the default frame that "Reset View" returns to
        this.ctx.strokeStyle = '#999999';
        this.ctx.lineWidth = 3 / this.scale; // Thicker border, more visible
        this.ctx.strokeRect(0, 0, window.innerWidth / this.scale, window.innerHeight / this.scale);

        // Add a subtle fill to make it more obvious
        this.ctx.fillStyle = 'rgba(200, 200, 200, 0.05)';
        this.ctx.fillRect(0, 0, window.innerWidth / this.scale, window.innerHeight / this.scale);

        // Adaptive grid sizing based on zoom level
        const baseGridSize = 50;
        let gridSize = baseGridSize;

        // Increase grid spacing when zoomed out for performance
        if (this.scale < 0.5) {
            gridSize = baseGridSize * 4; // 200px spacing when very zoomed out
        } else if (this.scale < 0.8) {
            gridSize = baseGridSize * 2; // 100px spacing when moderately zoomed out
        }

        // Calculate visible world bounds
        const worldLeft = -this.offsetX / this.scale;
        const worldTop = -this.offsetY / this.scale;
        const worldRight = worldLeft + (window.innerWidth / this.scale);
        const worldBottom = worldTop + (window.innerHeight / this.scale);

        // Set up grid drawing style
        const fontSize = Math.max(6, Math.min(14, 12 / this.scale));
        this.ctx.font = `${fontSize}px Arial`;
        this.ctx.fillStyle = '#d0d0d0';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        // Calculate grid bounds in world coordinates with smaller buffer for performance
        const bufferSize = gridSize * 2; // Reduced buffer
        const startX = Math.floor((worldLeft - bufferSize) / gridSize) * gridSize;
        const startY = Math.floor((worldTop - bufferSize) / gridSize) * gridSize;
        const endX = Math.ceil((worldRight + bufferSize) / gridSize) * gridSize;
        const endY = Math.ceil((worldBottom + bufferSize) / gridSize) * gridSize;

        // Limit maximum number of grid points for performance
        const maxGridPoints = 2000;
        const totalPoints = ((endX - startX) / gridSize) * ((endY - startY) / gridSize);

        if (totalPoints <= maxGridPoints) {
            // Draw grid points
            for (let x = startX; x <= endX; x += gridSize) {
                for (let y = startY; y <= endY; y += gridSize) {
                    this.ctx.fillText('+', x, y);
                }
            }
        } else {
            // If too many points, increase spacing further
            const skipFactor = Math.ceil(Math.sqrt(totalPoints / maxGridPoints));
            const adjustedGridSize = gridSize * skipFactor;

            const adjustedStartX = Math.floor((worldLeft - bufferSize) / adjustedGridSize) * adjustedGridSize;
            const adjustedStartY = Math.floor((worldTop - bufferSize) / adjustedGridSize) * adjustedGridSize;
            const adjustedEndX = Math.ceil((worldRight + bufferSize) / adjustedGridSize) * adjustedGridSize;
            const adjustedEndY = Math.ceil((worldBottom + bufferSize) / adjustedGridSize) * adjustedGridSize;

            for (let x = adjustedStartX; x <= adjustedEndX; x += adjustedGridSize) {
                for (let y = adjustedStartY; y <= adjustedEndY; y += adjustedGridSize) {
                    this.ctx.fillText('+', x, y);
                }
            }
        }

        // Restore context
        this.ctx.restore();
    }
    

    
    selectTool(e) {
        document.querySelectorAll('.tool-button').forEach(btn => btn.classList.remove('active'));
        e.target.classList.add('active');
        this.currentTool = e.target.dataset.tool;
        
        // Show/hide tool options
        if (['pen', 'text'].includes(this.currentTool)) {
            this.toolOptions.classList.add('visible');
        } else {
            this.toolOptions.classList.remove('visible');
        }
        
        // Handle file uploads
        if (this.currentTool === 'image') {
            document.getElementById('image-input').click();
        } else if (this.currentTool === 'video') {
            document.getElementById('video-input').click();
        }
        
        // Update cursor
        this.updateCursor();
    }
    
    updateCursor() {
        const cursors = {
            hand: 'grab',
            select: 'default',
            pen: 'crosshair',
            text: 'text',
            crop: 'crosshair'
        };
        
        this.container.style.cursor = cursors[this.currentTool] || 'default';
    }
    
    handleMouseDown(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left - this.offsetX) / this.scale;
        const y = (e.clientY - rect.top - this.offsetY) / this.scale;

        this.lastX = x;
        this.lastY = y;

        // Handle pending upload placement
        if (this.pendingUpload) {
            this.placePendingUpload(x, y);
            return;
        }

        // Check if clicking on a resize handle
        if (e.target.classList.contains('resize-handle')) {
            this.startResize(e);
            return;
        }

        // Middle mouse button for panning (like hand tool)
        if (e.button === 1) {
            this.isMiddleMouseDragging = true;
            this.container.classList.add('grabbing');
            e.preventDefault();
            return;
        }

        if (this.currentTool === 'hand' && e.button === 0) {
            this.isDragging = true;
            this.container.classList.add('grabbing');
        } else if (this.currentTool === 'pen' && e.button === 0) {
            this.isDrawing = true;

            // Save context and apply transformations for drawing
            this.ctx.save();
            this.ctx.scale(this.scale, this.scale);
            this.ctx.translate(this.offsetX / this.scale, this.offsetY / this.scale);

            this.ctx.beginPath();
            this.ctx.moveTo(x, y);

            this.ctx.restore();
        } else if (this.currentTool === 'text' && e.button === 0) {
            this.addText(x, y);
        } else if (this.currentTool === 'select' && e.button === 0) {
            this.handleSelection(x, y);
        }
    }
    
    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left - this.offsetX) / this.scale;
        const y = (e.clientY - rect.top - this.offsetY) / this.scale;

        // Update mouse position for pending uploads
        this.mouseX = e.clientX;
        this.mouseY = e.clientY;

        // Update pending upload position
        if (this.pendingUpload) {
            this.updatePendingUploadPosition();
        }

        // Handle resizing
        if (this.isResizing) {
            this.handleResize(e);
            return;
        }

        // Handle middle mouse dragging
        if (this.isMiddleMouseDragging) {
            const deltaX = (x - this.lastX) * this.scale;
            const deltaY = (y - this.lastY) * this.scale;

            this.offsetX += deltaX;
            this.offsetY += deltaY;

            this.drawGrid();
            this.updateElementPositions();
        } else if (this.isDragging && this.currentTool === 'hand') {
            const deltaX = (x - this.lastX) * this.scale;
            const deltaY = (y - this.lastY) * this.scale;

            this.offsetX += deltaX;
            this.offsetY += deltaY;

            this.drawGrid();
            this.updateElementPositions();
        } else if (this.isDrawing && this.currentTool === 'pen') {
            // Save context and apply transformations for drawing
            this.ctx.save();
            this.ctx.scale(this.scale, this.scale);
            this.ctx.translate(this.offsetX / this.scale, this.offsetY / this.scale);

            this.ctx.lineWidth = this.brushSize / this.scale;
            this.ctx.strokeStyle = this.brushColor;
            this.ctx.lineCap = 'round';
            this.ctx.lineJoin = 'round';

            this.ctx.lineTo(x, y);
            this.ctx.stroke();
            this.ctx.beginPath();
            this.ctx.moveTo(x, y);

            this.ctx.restore();
        }

        this.lastX = x;
        this.lastY = y;
    }
    
    handleMouseUp() {
        this.isDragging = false;
        this.isDrawing = false;
        this.isMiddleMouseDragging = false;
        this.isResizing = false;
        this.resizeHandle = null;
        this.container.classList.remove('grabbing');
    }
    
    handleWheel(e) {
        e.preventDefault();

        if (e.ctrlKey || e.metaKey) {
            // Ctrl/Cmd + scroll wheel = zoom
            const mouseX = e.clientX;
            const mouseY = e.clientY;

            // Consistent zoom direction: scroll down = zoom out, scroll up = zoom in
            const zoomIn = e.deltaY < 0;
            const zoomFactor = 1.05; // Much slower zoom (was 1.2)

            const newScale = zoomIn ?
                Math.min(5, this.scale * zoomFactor) :
                Math.max(0.05, this.scale / zoomFactor);

            if (newScale !== this.scale) {
                // Calculate world coordinates of mouse position BEFORE zoom
                const worldMouseX = (mouseX - this.offsetX) / this.scale;
                const worldMouseY = (mouseY - this.offsetY) / this.scale;

                // Update scale
                this.scale = newScale;

                // Calculate new offset to keep the world point under the mouse
                // Formula: newOffset = mouseScreen - worldMouse * newScale
                this.offsetX = mouseX - (worldMouseX * this.scale);
                this.offsetY = mouseY - (worldMouseY * this.scale);

                this.drawGrid();
                this.updateElementPositions();
            }
        } else {
            // Normal scroll wheel = vertical scrolling
            const scrollSpeed = 50;
            this.offsetY -= e.deltaY > 0 ? scrollSpeed : -scrollSpeed;

            this.drawGrid();
            this.updateElementPositions();
        }
    }
    
    resetView() {
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;

        this.drawGrid();
        this.updateElementPositions();
    }
    
    handleResize() {
        this.setupCanvas();
        this.drawGrid();
        this.updateElementPositions();
    }
    
    handleKeyDown(e) {
        if (e.key === 'Delete' && this.selectedElement) {
            this.deleteSelectedElement();
        }
    }
    
    addText(x, y) {
        const text = prompt('Enter text:');
        if (text) {
            const element = {
                type: 'text',
                x: x,
                y: y,
                width: text.length * this.fontSize * 0.6,
                height: this.fontSize * 1.2,
                content: text,
                fontSize: this.fontSize,
                color: this.brushColor
            };
            
            this.elements.push(element);
            this.renderElements();
        }
    }
    
    handleImageUpload(e) {
        const file = e.target.files[0];
        if (file) {
            this.trackFileSize(file);

            const reader = new FileReader();
            reader.onload = (event) => {
                const img = new Image();
                img.onload = () => {
                    // Create pending upload that follows mouse
                    this.pendingUpload = {
                        type: 'image',
                        width: Math.min(img.width, 300), // Limit initial size
                        height: Math.min(img.height, 300),
                        src: event.target.result,
                        file: file
                    };

                    this.createPendingUploadElement();
                };
                img.src = event.target.result;
            };
            reader.readAsDataURL(file);
        }
        // Reset file input
        e.target.value = '';
    }
    
    handleVideoUpload(e) {
        const file = e.target.files[0];
        if (file) {
            this.trackFileSize(file);

            const reader = new FileReader();
            reader.onload = (event) => {
                // Create pending upload that follows mouse
                this.pendingUpload = {
                    type: 'video',
                    width: 320,
                    height: 240,
                    src: event.target.result,
                    file: file
                };

                this.createPendingUploadElement();
            };
            reader.readAsDataURL(file);
        }
        // Reset file input
        e.target.value = '';
    }
    
    trackFileSize(file) {
        this.totalFileSize += file.size;
        console.log(`File uploaded: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
        console.log(`Total file size: ${(this.totalFileSize / 1024 / 1024).toFixed(2)} MB`);
    }
    
    renderElements() {
        // This would render elements on the canvas or as DOM elements
        // For now, we'll create DOM elements for images and videos
        this.elements.forEach((element) => {
            if (!element.domElement) {
                this.createElement(element);
            }
        });
    }

    createElement(element) {
        const div = document.createElement('div');
        div.className = 'canvas-element';

        // Use transform for smoother positioning
        const x = element.x * this.scale + this.offsetX;
        const y = element.y * this.scale + this.offsetY;
        const width = element.width * this.scale;
        const height = element.height * this.scale;

        div.style.position = 'absolute';
        div.style.left = '0px';
        div.style.top = '0px';
        div.style.transform = `translate(${x}px, ${y}px)`;
        div.style.width = width + 'px';
        div.style.height = height + 'px';
        
        if (element.type === 'image') {
            const img = document.createElement('img');
            img.src = element.src;
            div.appendChild(img);
        } else if (element.type === 'video') {
            const video = document.createElement('video');
            video.src = element.src;
            video.controls = false;
            div.appendChild(video);
            
            // Add custom controls
            this.addVideoControls(div, video);
        }
        
        element.domElement = div;
        this.container.appendChild(div);
    }
    
    addVideoControls(container, video) {
        const controls = document.createElement('div');
        controls.className = 'video-controls';
        
        const playButton = document.createElement('button');
        playButton.className = 'play-button';
        playButton.textContent = '▶️';
        playButton.onclick = () => {
            if (video.paused) {
                video.play();
                playButton.textContent = '⏸️';
            } else {
                video.pause();
                playButton.textContent = '▶️';
            }
        };
        
        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar';
        const progressFill = document.createElement('div');
        progressFill.className = 'progress-fill';
        progressBar.appendChild(progressFill);
        
        const timeDisplay = document.createElement('span');
        timeDisplay.className = 'time-display';
        timeDisplay.textContent = '0:00 / 0:00';
        
        video.addEventListener('timeupdate', () => {
            const progress = (video.currentTime / video.duration) * 100;
            progressFill.style.width = progress + '%';
            
            const current = this.formatTime(video.currentTime);
            const duration = this.formatTime(video.duration);
            timeDisplay.textContent = `${current} / ${duration}`;
        });
        
        controls.appendChild(playButton);
        controls.appendChild(progressBar);
        controls.appendChild(timeDisplay);
        container.appendChild(controls);
    }
    
    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }
    
    handleSelection(x, y) {
        // Find element at position
        const element = this.findElementAt(x, y);

        // Clear previous selection
        if (this.selectedElement) {
            this.selectedElement.domElement.classList.remove('selected');
            this.removeResizeHandles();
        }

        if (element) {
            this.selectedElement = element;
            element.domElement.classList.add('selected');
            this.addResizeHandles(element);
        } else {
            this.selectedElement = null;
        }
    }
    
    findElementAt(x, y) {
        return this.elements.find(element => 
            x >= element.x && x <= element.x + element.width &&
            y >= element.y && y <= element.y + element.height
        );
    }
    
    addResizeHandles(element) {
        // Remove existing handles first
        this.removeResizeHandles();

        const handles = ['nw', 'ne', 'sw', 'se'];
        handles.forEach(position => {
            const handle = document.createElement('div');
            handle.className = `resize-handle ${position}`;
            handle.addEventListener('mousedown', this.startResize.bind(this));
            element.domElement.appendChild(handle);
        });
    }

    removeResizeHandles() {
        document.querySelectorAll('.resize-handle').forEach(handle => handle.remove());
    }
    
    deleteSelectedElement() {
        if (this.selectedElement) {
            const index = this.elements.indexOf(this.selectedElement);
            if (index > -1) {
                this.removeResizeHandles();
                this.selectedElement.domElement.remove();
                this.elements.splice(index, 1);
                this.selectedElement = null;
            }
        }
    }

    createPendingUploadElement() {
        if (this.pendingUpload.domElement) {
            this.pendingUpload.domElement.remove();
        }

        const div = document.createElement('div');
        div.className = 'canvas-element pending-upload';
        div.style.position = 'fixed';
        div.style.pointerEvents = 'none';
        div.style.opacity = '0.7';
        div.style.border = '2px dashed #0066cc';
        div.style.zIndex = '9999';
        div.style.width = this.pendingUpload.width + 'px';
        div.style.height = this.pendingUpload.height + 'px';

        if (this.pendingUpload.type === 'image') {
            const img = document.createElement('img');
            img.src = this.pendingUpload.src;
            img.style.width = '100%';
            img.style.height = '100%';
            img.style.objectFit = 'contain';
            div.appendChild(img);
        } else if (this.pendingUpload.type === 'video') {
            const video = document.createElement('video');
            video.src = this.pendingUpload.src;
            video.style.width = '100%';
            video.style.height = '100%';
            video.style.objectFit = 'contain';
            div.appendChild(video);
        }

        this.pendingUpload.domElement = div;
        document.body.appendChild(div);
        this.updatePendingUploadPosition();
    }

    updatePendingUploadPosition() {
        if (this.pendingUpload && this.pendingUpload.domElement) {
            this.pendingUpload.domElement.style.left = (this.mouseX - this.pendingUpload.width / 2) + 'px';
            this.pendingUpload.domElement.style.top = (this.mouseY - this.pendingUpload.height / 2) + 'px';
        }
    }

    placePendingUpload(x, y) {
        if (this.pendingUpload) {
            // Remove the pending element
            this.pendingUpload.domElement.remove();

            // Create the actual element
            const element = {
                type: this.pendingUpload.type,
                x: x - this.pendingUpload.width / 2 / this.scale,
                y: y - this.pendingUpload.height / 2 / this.scale,
                width: this.pendingUpload.width / this.scale,
                height: this.pendingUpload.height / this.scale,
                src: this.pendingUpload.src,
                file: this.pendingUpload.file
            };

            this.elements.push(element);
            this.createElement(element);

            // Clear pending upload
            this.pendingUpload = null;
        }
    }

    updateElementPositions() {
        this.elements.forEach(element => {
            if (element.domElement) {
                // Use transform instead of left/top for smoother movement
                const x = element.x * this.scale + this.offsetX;
                const y = element.y * this.scale + this.offsetY;
                const width = element.width * this.scale;
                const height = element.height * this.scale;

                element.domElement.style.transform = `translate(${x}px, ${y}px)`;
                element.domElement.style.width = width + 'px';
                element.domElement.style.height = height + 'px';

                // Ensure element is positioned absolutely
                element.domElement.style.position = 'absolute';
                element.domElement.style.left = '0px';
                element.domElement.style.top = '0px';
            }
        });
    }

    startResize(e) {
        e.preventDefault();
        e.stopPropagation();

        this.isResizing = true;
        this.resizeHandle = e.target.classList[1]; // Get handle position (nw, ne, sw, se)

        // Store initial mouse position
        this.resizeStartX = e.clientX;
        this.resizeStartY = e.clientY;

        // Store initial element dimensions and position
        this.resizeStartWidth = this.selectedElement.width;
        this.resizeStartHeight = this.selectedElement.height;
        this.resizeStartElementX = this.selectedElement.x;
        this.resizeStartElementY = this.selectedElement.y;
    }

    handleResize(e) {
        if (!this.isResizing || !this.selectedElement) return;

        const deltaX = (e.clientX - this.resizeStartX) / this.scale;
        const deltaY = (e.clientY - this.resizeStartY) / this.scale;

        let newWidth = this.resizeStartWidth;
        let newHeight = this.resizeStartHeight;
        let newX = this.resizeStartElementX;
        let newY = this.resizeStartElementY;

        // Calculate new dimensions based on handle position
        switch (this.resizeHandle) {
            case 'se': // Bottom-right
                newWidth = Math.max(20, this.resizeStartWidth + deltaX);
                newHeight = Math.max(20, this.resizeStartHeight + deltaY);
                break;
            case 'sw': // Bottom-left
                newWidth = Math.max(20, this.resizeStartWidth - deltaX);
                newHeight = Math.max(20, this.resizeStartHeight + deltaY);
                newX = this.resizeStartElementX + (this.resizeStartWidth - newWidth);
                break;
            case 'ne': // Top-right
                newWidth = Math.max(20, this.resizeStartWidth + deltaX);
                newHeight = Math.max(20, this.resizeStartHeight - deltaY);
                newY = this.resizeStartElementY + (this.resizeStartHeight - newHeight);
                break;
            case 'nw': // Top-left
                newWidth = Math.max(20, this.resizeStartWidth - deltaX);
                newHeight = Math.max(20, this.resizeStartHeight - deltaY);
                newX = this.resizeStartElementX + (this.resizeStartWidth - newWidth);
                newY = this.resizeStartElementY + (this.resizeStartHeight - newHeight);
                break;
        }

        // Update element properties
        this.selectedElement.width = newWidth;
        this.selectedElement.height = newHeight;
        this.selectedElement.x = newX;
        this.selectedElement.y = newY;

        // Update DOM element with smooth transform
        const x = newX * this.scale + this.offsetX;
        const y = newY * this.scale + this.offsetY;
        const width = newWidth * this.scale;
        const height = newHeight * this.scale;

        this.selectedElement.domElement.style.transform = `translate(${x}px, ${y}px)`;
        this.selectedElement.domElement.style.width = width + 'px';
        this.selectedElement.domElement.style.height = height + 'px';
    }
}

// Initialize the whiteboard when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new InfiniteWhiteboard();
});
