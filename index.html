<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infinite Whiteboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            overflow: hidden;
            height: 100vh;
            color: #ffffff;
        }

        #canvas-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            cursor: grab;
        }

        #canvas-container.grabbing {
            cursor: grabbing;
        }

        #whiteboard-canvas {
            position: absolute;
            background: #ffffff;
            cursor: inherit;
        }

        .starting-frame {
            position: absolute;
            border: 2px solid #cccccc;
            pointer-events: none;
            z-index: 1;
        }

        .toolbox {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #2a2a2a;
            border: 1px solid #404040;
            border-radius: 12px;
            padding: 12px;
            display: flex;
            gap: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            cursor: move;
            user-select: none;
        }

        .tool-button {
            width: 40px;
            height: 40px;
            background: #3a3a3a;
            border: 1px solid #505050;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #ffffff;
            font-size: 16px;
        }

        .tool-button:hover {
            background: #4a4a4a;
            border-color: #606060;
        }

        .tool-button.active {
            background: #0066cc;
            border-color: #0088ff;
        }

        .tool-options {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #2a2a2a;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 12px;
            display: none;
            gap: 8px;
            flex-direction: column;
            z-index: 1001;
        }

        .tool-options.visible {
            display: flex;
        }

        .option-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .option-label {
            font-size: 12px;
            color: #cccccc;
            min-width: 60px;
        }

        input[type="range"], input[type="color"], input[type="number"] {
            background: #3a3a3a;
            border: 1px solid #505050;
            border-radius: 4px;
            color: #ffffff;
            padding: 4px;
        }

        input[type="color"] {
            width: 40px;
            height: 30px;
            padding: 2px;
        }

        .reset-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #3a3a3a;
            border: 1px solid #505050;
            border-radius: 8px;
            padding: 8px 16px;
            color: #ffffff;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .reset-button:hover {
            background: #4a4a4a;
        }

        .file-input {
            display: none;
        }

        .canvas-element {
            position: absolute;
            user-select: none;
        }

        .canvas-element.selected {
            outline: 2px solid #0066cc;
            outline-offset: 2px;
        }

        .canvas-element img, .canvas-element video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .resize-handle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #0066cc;
            border: 1px solid #ffffff;
            border-radius: 50%;
        }

        .resize-handle.nw { top: -4px; left: -4px; cursor: nw-resize; }
        .resize-handle.ne { top: -4px; right: -4px; cursor: ne-resize; }
        .resize-handle.sw { bottom: -4px; left: -4px; cursor: sw-resize; }
        .resize-handle.se { bottom: -4px; right: -4px; cursor: se-resize; }

        .video-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            padding: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .play-button {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }

        .progress-bar {
            flex: 1;
            height: 4px;
            background: #555;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #0066cc;
            width: 0%;
            transition: width 0.1s ease;
        }

        .time-display {
            font-size: 10px;
            color: #ccc;
        }

        .pending-upload {
            pointer-events: none !important;
            opacity: 0.7 !important;
            border: 2px dashed #0066cc !important;
            z-index: 9999 !important;
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <canvas id="whiteboard-canvas"></canvas>
        <div class="starting-frame"></div>
    </div>

    <div class="toolbox" id="toolbox">
        <div class="tool-button active" data-tool="hand" title="Hand Tool">✋</div>
        <div class="tool-button" data-tool="select" title="Select Tool">⬆️</div>
        <div class="tool-button" data-tool="pen" title="Pen Tool">✏️</div>
        <div class="tool-button" data-tool="text" title="Text Tool">T</div>
        <div class="tool-button" data-tool="crop" title="Crop Tool">✂️</div>
        <div class="tool-button" data-tool="image" title="Image Upload">🖼️</div>
        <div class="tool-button" data-tool="video" title="Video Upload">🎥</div>
    </div>

    <div class="tool-options" id="tool-options">
        <div class="option-group">
            <span class="option-label">Size:</span>
            <input type="range" id="brush-size" min="1" max="50" value="3">
            <span id="size-value">3</span>
        </div>
        <div class="option-group">
            <span class="option-label">Color:</span>
            <input type="color" id="brush-color" value="#000000">
        </div>
        <div class="option-group">
            <span class="option-label">Font Size:</span>
            <input type="number" id="font-size" min="8" max="72" value="16">
        </div>
    </div>

    <div class="reset-button" id="reset-view">Reset View</div>

    <input type="file" id="image-input" class="file-input" accept="image/*">
    <input type="file" id="video-input" class="file-input" accept="video/*">

    <script src="whiteboard.js"></script>
</body>
</html>
