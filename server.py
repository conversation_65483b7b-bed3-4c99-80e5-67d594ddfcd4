#!/usr/bin/env python3
"""
Simple HTTP server for testing the infinite whiteboard application.
Serves static files and provides basic file upload testing capabilities.
"""

import http.server
import socketserver
import os
import sys
import webbrowser
from urllib.parse import urlparse
import json
import cgi
import tempfile
import shutil

class WhiteboardHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler for the whiteboard application."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def do_GET(self):
        """Handle GET requests."""
        if self.path == '/':
            self.path = '/index.html'
        return super().do_GET()
    
    def do_POST(self):
        """Handle POST requests for file uploads (for future use)."""
        if self.path == '/upload':
            self.handle_upload()
        else:
            self.send_error(404, "Not Found")
    
    def handle_upload(self):
        """Handle file upload requests."""
        try:
            # Parse the form data
            content_type = self.headers['content-type']
            if not content_type.startswith('multipart/form-data'):
                self.send_error(400, "Bad Request: Expected multipart/form-data")
                return
            
            # Get content length
            content_length = int(self.headers['Content-Length'])
            
            # Read the form data
            form_data = cgi.FieldStorage(
                fp=self.rfile,
                headers=self.headers,
                environ={
                    'REQUEST_METHOD': 'POST',
                    'CONTENT_TYPE': content_type,
                    'CONTENT_LENGTH': content_length
                }
            )
            
            uploaded_files = []
            
            # Process uploaded files
            for field_name in form_data:
                field = form_data[field_name]
                if hasattr(field, 'filename') and field.filename:
                    # Create uploads directory if it doesn't exist
                    uploads_dir = os.path.join(os.getcwd(), 'uploads')
                    os.makedirs(uploads_dir, exist_ok=True)
                    
                    # Save the file
                    file_path = os.path.join(uploads_dir, field.filename)
                    with open(file_path, 'wb') as f:
                        shutil.copyfileobj(field.file, f)
                    
                    file_size = os.path.getsize(file_path)
                    uploaded_files.append({
                        'filename': field.filename,
                        'size': file_size,
                        'path': file_path
                    })
                    
                    print(f"File uploaded: {field.filename} ({file_size} bytes)")
            
            # Send response
            response = {
                'status': 'success',
                'files': uploaded_files,
                'message': f'Successfully uploaded {len(uploaded_files)} file(s)'
            }
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))
            
        except Exception as e:
            print(f"Upload error: {str(e)}")
            self.send_error(500, f"Internal Server Error: {str(e)}")
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS."""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """Custom log message format."""
        print(f"[{self.log_date_time_string()}] {format % args}")

def find_free_port(start_port=8000, max_attempts=100):
    """Find a free port starting from start_port."""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"Could not find a free port in range {start_port}-{start_port + max_attempts}")

def main():
    """Main function to start the server."""
    try:
        # Find a free port
        port = find_free_port()
        
        # Create server
        with socketserver.TCPServer(("", port), WhiteboardHTTPRequestHandler) as httpd:
            print(f"🎨 Infinite Whiteboard Server")
            print(f"📁 Serving files from: {os.getcwd()}")
            print(f"🌐 Server running at: http://localhost:{port}")
            print(f"📂 Uploads will be saved to: {os.path.join(os.getcwd(), 'uploads')}")
            print(f"🔧 Press Ctrl+C to stop the server")
            print("-" * 50)
            
            # Try to open browser automatically
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🚀 Opening browser automatically...")
            except Exception as e:
                print(f"⚠️  Could not open browser automatically: {e}")
                print(f"   Please open http://localhost:{port} manually")
            
            print("-" * 50)
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
